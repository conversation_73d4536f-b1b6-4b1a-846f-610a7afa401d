"""
豆包视觉识别插件
参考aiglass项目的实现，从API端动态获取豆包视觉大模型配置
"""

import asyncio
import base64
import aiohttp
import os
from typing import Optional, Dict, Any
from dataclasses import dataclass

from loguru import logger
from plugins_func.register import register_function, Action, ActionResponse, ToolType
from core.handle.sendAudioHandle import send_tts_message

TAG = __name__

# 常量定义
DEFAULT_API_KEY = ""
DEFAULT_MODEL = "ep-20241230140547-8xqzr"
DEFAULT_BASE_URL = "https://ark.cn-beijing.volces.com/api/v3"
DEFAULT_MAX_TOKENS = 1000
DEFAULT_TEMPERATURE = 0.7

# 默认提示词配置
DEFAULT_PROMPTS = {
    "1": "请详细描述这张图片中的内容，包括物体、人物、场景、颜色等细节。",
    "2": "请分析这张图片中的路况信息，包括道路状况、交通标志、障碍物等，并提供导航建议。",
    "3": "请识别这张图片中的文字内容，并进行OCR文字识别。",
    "4": "请分析这张图片中的物体和场景，判断是否存在安全隐患或需要注意的事项。",
    "5": "请从专业角度分析这张图片，提供详细的技术性描述和建议。"
}

@dataclass
class VisionConfig:
    """视觉配置数据类"""
    api_key: str = ""
    base_url: str = DEFAULT_BASE_URL
    model: str = DEFAULT_MODEL
    max_tokens: int = DEFAULT_MAX_TOKENS
    temperature: float = DEFAULT_TEMPERATURE
    prompts: Optional[Dict[str, str]] = None

    def __post_init__(self):
        if self.prompts is None:
            self.prompts = DEFAULT_PROMPTS.copy()

    @property
    def is_valid(self) -> bool:
        """检查配置是否有效"""
        return bool(self.api_key and self.model)


def fetch_llm_config_from_server(llm_code: str) -> Optional[Dict[str, Any]]:
    """从服务器获取LLM配置"""
    if not llm_code:
        logger.bind(tag=TAG).warning("LLM编码为空，无法从服务器获取配置")
        return None

    logger.bind(tag=TAG).info(f"从服务器获取LLM配置: {llm_code}")

    try:
        from config.manage_api_client import ManageApiClient

        if not hasattr(ManageApiClient, '_instance') or ManageApiClient._instance is None:
            logger.bind(tag=TAG).warning("ManageApiClient未初始化")
            return None

        response = _execute_api_request(llm_code)
        if not response:
            return None

        return _parse_server_response(response)

    except Exception as e:
        if _is_resource_not_found_error(e):
            logger.bind(tag=TAG).warning(f"服务器上未找到模型编码 {llm_code} 对应的配置")
            return None

        logger.bind(tag=TAG).error(f"获取LLM配置时出错: {e}")
        return None


def _execute_api_request(llm_code: str) -> Optional[Dict[str, Any]]:
    """执行API请求"""
    from config.manage_api_client import ManageApiClient

    try:
        if not hasattr(ManageApiClient, '_instance') or ManageApiClient._instance is None:
            logger.bind(tag=TAG).warning("ManageApiClient未初始化")
            return None

        response = ManageApiClient._instance._execute_request("GET", f"/config/model/{llm_code}")
        if not response:
            logger.bind(tag=TAG).warning("服务器返回空响应")
            return None
        return response
    except Exception as e:
        if _is_resource_not_found_error(e):
            raise
        logger.bind(tag=TAG).error(f"API请求失败: {e}")
        return None


def _is_resource_not_found_error(error: Exception) -> bool:
    """检查是否为资源不存在错误"""
    error_msg = str(error)
    return "资源不存在" in error_msg or "未找到对应的模型数据" in error_msg


def _parse_server_response(response: Dict[str, Any]) -> Dict[str, Any]:
    """解析服务器响应"""
    logger.bind(tag=TAG).info(f"解析服务器响应: {response}")

    config_json = response.get("configJson", {})

    # 构建基础配置
    llm_config = {
        "type": config_json.get("type", "openai"),
        "model_name": config_json.get("model_name", response.get("modelName")),
        "api_key": config_json.get("api_key")
    }

    # 添加可选字段
    if "base_url" in config_json:
        llm_config["base_url"] = config_json["base_url"]

    # 添加prompt字段
    for i in range(1, 6):
        prompt_key = f"prompt{i}"
        if prompt_key in config_json:
            llm_config[prompt_key] = config_json[prompt_key]

    # 应用默认值
    _apply_default_values(llm_config)

    logger.bind(tag=TAG).info(f"转换后的LLM配置: {llm_config}")
    return llm_config


def _apply_default_values(llm_config: Dict[str, Any]) -> None:
    """应用默认值"""
    if not llm_config.get("api_key"):
        logger.bind(tag=TAG).warning("API密钥为空，使用默认值")
        llm_config["api_key"] = DEFAULT_API_KEY

    model_name = llm_config.get("model_name")
    if not model_name or model_name == "豆包识图模型":
        logger.bind(tag=TAG).warning("模型名称无效，使用默认值")
        llm_config["model_name"] = DEFAULT_MODEL

class DoubaoVisionProvider:
    """豆包视觉大模型提供者"""

    def __init__(self, conn):
        """初始化豆包视觉提供者"""
        self.conn = conn
        self.config = VisionConfig()
        self._connection_tested = False

        # 加载配置
        self._initialize_config()

    def _initialize_config(self) -> None:
        """初始化配置"""
        # 如果启用了API配置，优先从服务器加载
        if self.conn.config.get("read_config_from_api", False):
            if self._load_server_config():
                return
            # 服务器配置失败时，尝试本地配置作为备用
            if self._load_local_config():
                return
        else:
            # 未启用API配置时，只使用本地配置
            if self._load_local_config():
                return

        logger.bind(tag=TAG).warning("使用默认配置")

    def _load_local_config(self) -> bool:
        """从本地配置加载"""
        llm_code = self._get_llm_code()
        if not llm_code:
            logger.bind(tag=TAG).warning("未配置 llm_code，无法加载本地配置")
            return False

        llm_configs = self.conn.config.get("LLM", {})
        llm_config = llm_configs.get(llm_code, {})

        if not llm_config:
            logger.bind(tag=TAG).warning(f"本地未找到 {llm_code} 的配置")
            return False

        if not llm_config.get("api_key"):
            logger.bind(tag=TAG).warning(f"本地配置 {llm_code} 缺少API密钥")
            return False

        self._apply_config(llm_config)
        self._load_prompts_from_config(llm_config)

        logger.bind(tag=TAG).info(f"从本地配置加载成功，模型: {self.config.model}")
        return True

    def _load_server_config(self) -> bool:
        """从服务器配置加载"""
        llm_code = self._get_llm_code()
        if not llm_code:
            logger.bind(tag=TAG).warning("未配置 llm_code，无法从服务器获取配置")
            return False

        try:
            llm_config = fetch_llm_config_from_server(llm_code)
            if not llm_config:
                logger.bind(tag=TAG).warning(f"服务器未返回 {llm_code} 的配置")
                return False

            if not llm_config.get("api_key"):
                logger.bind(tag=TAG).warning(f"服务器配置 {llm_code} 缺少API密钥")
                return False

            self._apply_config(llm_config)
            self._load_prompts_from_config(llm_config)

            logger.bind(tag=TAG).info(f"从服务器配置加载成功，模型: {self.config.model}")
            return True

        except Exception as e:
            logger.bind(tag=TAG).error(f"从服务器加载配置失败: {e}")
            return False

    def _get_llm_code(self) -> str:
        """获取LLM编码"""
        return (self.conn.config
                .get("plugins", {})
                .get("eye_doubao", {})
                .get("llm_code", ""))

    def _apply_config(self, llm_config: Dict[str, Any]) -> None:
        """应用配置"""
        self.config.api_key = llm_config.get("api_key", "")
        self.config.base_url = llm_config.get("base_url", DEFAULT_BASE_URL)
        self.config.model = llm_config.get("model_name", DEFAULT_MODEL)
        self.config.max_tokens = llm_config.get("max_tokens", DEFAULT_MAX_TOKENS)
        self.config.temperature = llm_config.get("temperature", DEFAULT_TEMPERATURE)

    def _load_prompts_from_config(self, llm_config: Dict[str, Any]) -> None:
        """从配置加载提示词"""
        prompts = {}

        # 首先尝试从LLM自定义属性获取
        if hasattr(self.conn, 'llm'):
            custom_attrs = getattr(self.conn.llm, 'custom_attrs', None)
            if custom_attrs:
                prompts = self._extract_prompts_from_attrs(custom_attrs)

        # 如果没有获取到，从配置中获取
        if not prompts:
            prompts = self._extract_prompts_from_config(llm_config)

        # 填充默认值
        for i in range(1, 6):
            key = str(i)
            if key not in prompts or not prompts[key]:
                prompts[key] = DEFAULT_PROMPTS.get(key, "请描述这张图片")

        self.config.prompts = prompts
        logger.bind(tag=TAG).info(f"加载提示词配置: {len(prompts)} 个")

    def _extract_prompts_from_attrs(self, custom_attrs: Dict[str, Any]) -> Dict[str, str]:
        """从自定义属性提取提示词"""
        prompts = {}
        for i in range(1, 6):
            prompt_key = f"prompt{i}"
            if prompt_key in custom_attrs and custom_attrs[prompt_key]:
                prompts[str(i)] = custom_attrs[prompt_key].strip()
        return prompts

    def _extract_prompts_from_config(self, llm_config: Dict[str, Any]) -> Dict[str, str]:
        """从配置提取提示词"""
        prompts = {}
        for i in range(1, 6):
            prompt_key = f"prompt{i}"
            if prompt_key in llm_config and llm_config[prompt_key]:
                prompts[str(i)] = llm_config[prompt_key].strip()
        return prompts

    @property
    def is_initialized(self) -> bool:
        """检查是否已初始化"""
        return self.config.is_valid

    def get_prompt_by_mode(self, mode: int) -> str:
        """根据模式获取提示词"""
        mode_str = str(mode)

        if not self.config.prompts:
            return "请描述这张图片"

        prompt = self.config.prompts.get(mode_str)
        if prompt:
            logger.bind(tag=TAG).info(f"使用模式 {mode} 的提示词: {prompt[:50]}...")
            return prompt

        # 使用默认提示词
        default_prompt = self.config.prompts.get("1", "请描述这张图片")
        logger.bind(tag=TAG).info(f"模式 {mode} 不存在，使用默认提示词")
        return default_prompt

    async def test_connection(self) -> bool:
        """测试API连通性"""
        if not self.is_initialized:
            logger.bind(tag=TAG).warning("配置无效，跳过连通性测试")
            return False

        logger.bind(tag=TAG).info("测试豆包视觉API连通性...")

        try:
            test_image_base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="

            payload = self._build_api_payload(
                "这是一个连通性测试，请简单回复'连接正常'",
                test_image_base64,
                max_tokens=50,
                temperature=0.1
            )

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.config.base_url}/chat/completions",
                    headers=self._get_headers(),
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    success = response.status == 200
                    if success:
                        result = await response.json()
                        content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                        logger.bind(tag=TAG).info(f"连通性测试成功: {content}")
                    else:
                        error_text = await response.text()
                        logger.bind(tag=TAG).error(f"连通性测试失败: {response.status}, {error_text}")
                    return success

        except asyncio.TimeoutError:
            logger.bind(tag=TAG).error("连通性测试超时")
            return False
        except Exception as e:
            logger.bind(tag=TAG).error(f"连通性测试异常: {e}")
            return False

    async def analyze_image(self, image_path: str, question: str = "请描述这张图片") -> Optional[str]:
        """分析图片"""
        if not self.is_initialized:
            logger.bind(tag=TAG).error("配置无效")
            return None

        # 首次使用时进行连通性测试
        if not self._connection_tested:
            logger.bind(tag=TAG).info("首次使用，进行连通性测试...")
            is_connected = await self.test_connection()
            self._connection_tested = True

            if is_connected:
                logger.bind(tag=TAG).info("✅ API连通性测试通过")
            else:
                logger.bind(tag=TAG).warning("⚠️ API连通性测试失败，但将继续尝试")

        # 读取并编码图片
        image_base64 = self._encode_image(image_path)
        if not image_base64:
            return None

        # 调用API
        return await self._call_vision_api(image_base64, question)

    def _encode_image(self, image_path: str) -> Optional[str]:
        """编码图片为base64"""
        if not os.path.exists(image_path):
            logger.bind(tag=TAG).error(f"图片文件不存在: {image_path}")
            return None

        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()
            return base64.b64encode(image_data).decode('utf-8')
        except Exception as e:
            logger.bind(tag=TAG).error(f"图片编码失败: {e}")
            return None

    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }

    def _build_api_payload(self, question: str, image_base64: str,
                          max_tokens: Optional[int] = None,
                          temperature: Optional[float] = None) -> Dict[str, Any]:
        """构建API请求载荷"""
        return {
            "model": self.config.model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": question},
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"}
                        }
                    ]
                }
            ],
            "max_tokens": max_tokens or self.config.max_tokens,
            "temperature": temperature or self.config.temperature
        }

    async def _call_vision_api(self, image_base64: str, question: str) -> Optional[str]:
        """调用视觉API"""
        try:
            payload = self._build_api_payload(question, image_base64)

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.config.base_url}/chat/completions",
                    headers=self._get_headers(),
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                        logger.bind(tag=TAG).info(f"API调用成功: {content[:100]}...")
                        return content
                    else:
                        error_text = await response.text()
                        logger.bind(tag=TAG).error(f"API调用失败: {response.status}, {error_text}")
                        return None

        except Exception as e:
            logger.bind(tag=TAG).error(f"API调用异常: {e}")
            return None




# 全局豆包视觉提供者实例缓存
_doubao_vision_providers = {}

def get_doubao_vision_provider(conn):
    """获取豆包视觉提供者实例"""
    global _doubao_vision_providers

    # 使用连接ID作为缓存键
    conn_id = getattr(conn, 'session_id', 'default')

    if conn_id not in _doubao_vision_providers:
        _doubao_vision_providers[conn_id] = DoubaoVisionProvider(conn)

    return _doubao_vision_providers[conn_id]


# 拍照识图函数描述
capture_and_analyze_image_desc = {
    "type": "function",
    "function": {
        "name": "capture_and_analyze_image",
        "description": "拍照并使用豆包视觉大模型进行图片识别",
        "parameters": {
            "type": "object",
            "properties": {
                "question": {
                    "type": "string",
                    "description": "对图片询问的问题，默认为'请描述这张图片'"
                }
            },
            "required": []
        }
    }
}

@register_function("capture_and_analyze_image", capture_and_analyze_image_desc, ToolType.IOT_CTL)
def capture_and_analyze_image(conn, question="请描述这张图片"):
    """拍照识图功能"""
    try:
        # 获取豆包视觉提供者
        vision_provider = get_doubao_vision_provider(conn)

        # 模拟图片路径（实际应该从摄像头获取）
        # 这里需要集成实际的图片获取逻辑
        image_path = "tmp/latest_camera_image.jpg"

        # 异步处理图片分析
        async def process_image():
            try:
                result = await vision_provider.analyze_image(image_path, question)
                if result:
                    # 通过TTS播报结果
                    await send_tts_message(conn, "start", result)
                    logger.bind(tag=TAG).info(f"图片识别结果已发送TTS: {result[:50]}...")
                else:
                    await send_tts_message(conn, "start", "抱歉，图片识别失败")
            except Exception as e:
                logger.bind(tag=TAG).error(f"处理图片识别时出错: {e}")
                await send_tts_message(conn, "start", "图片识别过程中发生错误")

        # 启动异步任务
        asyncio.create_task(process_image())

        return ActionResponse(
            action=Action.RESPONSE,
            result="success",
            response=f"正在拍照识图，问题：{question}"
        )

    except Exception as e:
        logger.bind(tag=TAG).error(f"拍照识图失败: {e}")
        return ActionResponse(
            action=Action.ERROR,
            result=str(e),
            response="拍照识图失败，请稍后重试"
        )


def eyes_doubaoreal(conn, mode, image_path):
    """
    豆包视觉识别主函数
    参考aiglass项目的实现

    Args:
        conn: 连接对象
        mode: 模式 (1=识图, 2=指路)
        image_path: 图片路径

    Returns:
        ActionResponse: 包含识别结果的响应对象
    """
    try:
        # 获取豆包视觉提供者
        vision_provider = get_doubao_vision_provider(conn)

        # 根据模式确定问题，使用配置的提示词
        question = vision_provider.get_prompt_by_mode(mode)

        # 异步处理图片分析
        async def process_vision():
            try:
                result = await vision_provider.analyze_image(image_path, question)
                if result:
                    # 通过TTS播报结果
                    await send_tts_message(conn, "start", result)
                    logger.bind(tag=TAG).info(f"豆包视觉识别完成: {result[:50]}...")
                    return ActionResponse(
                        action=Action.RESPONSE,
                        result=result,
                        response=result
                    )
                else:
                    error_msg = "豆包视觉识别失败"
                    await send_tts_message(conn, "start", error_msg)
                    return ActionResponse(
                        action=Action.ERROR,
                        result="vision_failed",
                        response=error_msg
                    )
            except Exception as e:
                error_msg = f"豆包视觉识别过程中发生错误: {e}"
                logger.bind(tag=TAG).error(error_msg)
                await send_tts_message(conn, "start", "视觉识别过程中发生错误")
                return ActionResponse(
                    action=Action.ERROR,
                    result=str(e),
                    response=error_msg
                )

        # 启动异步任务并返回初始响应
        asyncio.create_task(process_vision())

        return ActionResponse(
            action=Action.RESPONSE,
            result="processing",
            response="正在进行豆包视觉识别..."
        )

    except Exception as e:
        logger.bind(tag=TAG).error(f"eyes_doubaoreal失败: {e}")
        return ActionResponse(
            action=Action.ERROR,
            result=str(e),
            response="豆包视觉识别初始化失败"
        )





def _create_temp_connection(config):
    """创建临时连接对象"""
    class TempConnection:
        def __init__(self, config):
            self.config = config
            self.device_id = "startup_test"
            self.client_id = "startup_test"
            self.session_id = "startup_test"
    return TempConnection(config)


def _test_provider_connection(provider):
    """测试提供者连接"""
    async def run_test():
        is_connected = await provider.test_connection()
        if is_connected:
            logger.bind(tag=TAG).info("✅ 豆包视觉API连通性测试通过")
        else:
            logger.bind(tag=TAG).warning("⚠️ 豆包视觉API连通性测试失败")
        return is_connected

    try:
        import asyncio
        loop = asyncio.get_event_loop()
        if loop.is_running():
            asyncio.create_task(run_test())
        else:
            asyncio.run(run_test())
    except RuntimeError:
        logger.bind(tag=TAG).info("🔧 连通性测试将在首次使用时进行")


def _test_api_config(config):
    """测试API配置"""
    eye_doubao_config = config.get("plugins", {}).get("eye_doubao", {})
    llm_code = eye_doubao_config.get("llm_code", "")

    if not llm_code:
        logger.bind(tag=TAG).warning("未配置plugins.eye_doubao.llm_code")
        return

    logger.bind(tag=TAG).info(f"测试服务器配置: {llm_code}")

    llm_config = fetch_llm_config_from_server(llm_code)
    if not llm_config or not llm_config.get("api_key"):
        logger.bind(tag=TAG).warning(f"服务器配置 {llm_code} 无效或缺少API密钥")
        return

    temp_conn = _create_temp_connection(config)
    test_provider = DoubaoVisionProvider(temp_conn)

    # 手动设置配置
    test_provider.config.api_key = llm_config.get("api_key", "")
    test_provider.config.base_url = llm_config.get("base_url", DEFAULT_BASE_URL)
    test_provider.config.model = llm_config.get("model_name", DEFAULT_MODEL)
    test_provider.config.max_tokens = llm_config.get("max_tokens", DEFAULT_MAX_TOKENS)
    test_provider.config.temperature = llm_config.get("temperature", DEFAULT_TEMPERATURE)

    logger.bind(tag=TAG).info(f"使用服务器配置，模型: {test_provider.config.model}")
    _test_provider_connection(test_provider)


def _test_local_config(config):
    """测试本地配置"""
    eye_doubao_config = config.get("plugins", {}).get("eye_doubao", {})
    llm_code = eye_doubao_config.get("llm_code", "")

    if not llm_code:
        logger.bind(tag=TAG).info("💡 未配置plugins.eye_doubao.llm_code，请配置后重启")
        return

    llm_configs = config.get("LLM", {})
    llm_config = llm_configs.get(llm_code, {})

    if not llm_config.get("api_key"):
        logger.bind(tag=TAG).warning(f"本地配置 {llm_code} 缺少API密钥")
        return

    temp_conn = _create_temp_connection(config)
    test_provider = DoubaoVisionProvider(temp_conn)

    # 手动设置配置
    test_provider.config.api_key = llm_config.get("api_key", "")
    test_provider.config.base_url = llm_config.get("base_url", DEFAULT_BASE_URL)
    test_provider.config.model = llm_config.get("model_name", DEFAULT_MODEL)
    test_provider.config.max_tokens = llm_config.get("max_tokens", DEFAULT_MAX_TOKENS)
    test_provider.config.temperature = llm_config.get("temperature", DEFAULT_TEMPERATURE)

    logger.bind(tag=TAG).info(f"使用本地配置，模型: {test_provider.config.model}")
    _test_provider_connection(test_provider)


def _module_init():
    """模块加载时的初始化和连通性测试"""
    logger.bind(tag=TAG).info("🚀 豆包视觉模块已加载")

    try:
        from config.config_loader import load_config
        config = load_config()

        if config.get("read_config_from_api", False):
            logger.bind(tag=TAG).info("💡 配置从管理后台API获取")
            _test_api_config(config)
        else:
            logger.bind(tag=TAG).info("💡 使用本地配置")
            _test_local_config(config)

    except Exception as e:
        logger.bind(tag=TAG).warning(f"配置测试失败: {e}")

    logger.bind(tag=TAG).info("📝 支持的功能：拍照识图")


# 模块加载时执行初始化
try:
    _module_init()
except Exception:
    # 静默处理，不影响模块加载
    pass
