"""
豆包视觉识别插件
参考aiglass项目的实现，从API端动态获取豆包视觉大模型配置
"""

import asyncio
import base64
import aiohttp
import os
from plugins_func.register import register_function, Action, ActionResponse, ToolType
from core.handle.sendAudioHandle import send_tts_message



def fetch_llm_config_from_server(llm_code):
    """从服务器获取LLM配置，使用模型编码API

    Args:
        llm_code: LLM编码

    Returns:
        dict: LLM配置，如果获取失败则返回None
    """
    try:
        # 如果llm_code为None或空字符串，直接返回None
        if not llm_code:
            logger.bind(tag=TAG).warning("LLM编码为空，无法从服务器获取配置")
            return None

        logger.bind(tag=TAG).info(f"从服务器获取LLM配置: {llm_code}")

        # 检查ManageApiClient是否已初始化
        from config.manage_api_client import ManageApiClient
        if not hasattr(ManageApiClient, '_instance') or ManageApiClient._instance is None:
            logger.bind(tag=TAG).warning("ManageApiClient未初始化，无法从服务器获取配置")
            return None

        # 使用ManageApiClient发送请求
        try:
            response = ManageApiClient._instance._execute_request(
                "GET",
                f"/config/model/{llm_code}"
            )
        except Exception as e:
            # 如果是资源不存在的错误，记录警告并返回None
            if "资源不存在" in str(e) or "未找到对应的模型数据" in str(e):
                logger.bind(tag=TAG).warning(f"服务器上未找到模型编码 {llm_code} 对应的配置，将使用默认配置")
                return None
            # 其他错误重新抛出
            raise

        # 如果响应为None（可能是因为服务器返回了错误）
        if not response:
            logger.bind(tag=TAG).warning(f"获取模型配置失败，服务器返回空响应")
            return None

        logger.bind(tag=TAG).info(f"response配置: {response}")

        # 检查configJson字段是否存在
        config_json = response.get("configJson", {})

        # 从configJson中提取必要的字段
        llm_config = {
            "type": config_json.get("type", "openai"),
            "model_name": config_json.get("model_name", response.get("modelName")),
            "api_key": config_json.get("api_key")
        }

        # 添加可选字段
        if "base_url" in config_json:
            llm_config["base_url"] = config_json["base_url"]

        # 添加prompt1-5字段
        for i in range(1, 6):
            prompt_key = f"prompt{i}"
            if prompt_key in config_json:
                llm_config[prompt_key] = config_json[prompt_key]

        # 默认值
        DEFAULT_API_KEY = ""
        DEFAULT_MODEL = "ep-20241230140547-8xqzr"

        # 检查API密钥是否为空
        if not llm_config["api_key"]:
            logger.bind(tag=TAG).warning(f"从服务器获取的API密钥为空，将使用默认API密钥")
            llm_config["api_key"] = DEFAULT_API_KEY

        # 检查模型名称是否为空
        if not llm_config["model_name"] or llm_config["model_name"] == "豆包识图模型":
            logger.bind(tag=TAG).warning(f"从服务器获取的模型名称为空或不是实际模型名称，将使用默认模型名称")
            llm_config["model_name"] = DEFAULT_MODEL

        logger.bind(tag=TAG).info(f"转换后的LLM配置: {llm_config}")
        return llm_config

    except Exception as e:
        logger.bind(tag=TAG).error(f"获取LLM配置时出错: {e}")
        import traceback
        logger.bind(tag=TAG).error(f"错误详情: {traceback.format_exc()}")
        return None


from loguru import logger

TAG = __name__

# 默认提示词配置
DEFAULT_PROMPTS = {
    "1": "请详细描述这张图片中的内容，包括物体、人物、场景、颜色等细节。",
    "2": "请分析这张图片中的路况信息，包括道路状况、交通标志、障碍物等，并提供导航建议。",
    "3": "请识别这张图片中的文字内容，并进行OCR文字识别。",
    "4": "请分析这张图片中的物体和场景，判断是否存在安全隐患或需要注意的事项。",
    "5": "请从专业角度分析这张图片，提供详细的技术性描述和建议。"
}

class DoubaoVisionProvider:
    """豆包视觉大模型提供者"""

    def __init__(self, conn):
        """初始化豆包视觉提供者"""
        self.conn = conn
        self.api_key = ""
        self.base_url = ""
        self.model = ""
        self.max_tokens = 1000
        self.temperature = 0.7
        self.initialized = False
        self.custom_prompts = {}  # 存储自定义提示词

        # 从API获取配置
        self._load_config_from_api()

        # 获取提示词配置
        self._load_prompts_config()

    def _load_config_from_api(self):
        """从API端获取豆包视觉配置"""
        try:
            logger.bind(tag=TAG).info("开始加载豆包视觉配置...")

            # 打印完整的配置信息用于调试
            logger.bind(tag=TAG).info(f"连接配置信息: read_config_from_api={self.conn.config.get('read_config_from_api', False)}")

            # 首先尝试从plugins.eye_doubao.llm_code读取本地配置
            plugins_config = self.conn.config.get("plugins", {})
            logger.bind(tag=TAG).info(f"plugins配置: {plugins_config}")

            eye_doubao_config = plugins_config.get("eye_doubao", {})
            logger.bind(tag=TAG).info(f"eye_doubao配置: {eye_doubao_config}")

            llm_code = eye_doubao_config.get("llm_code", "")
            logger.bind(tag=TAG).info(f"llm_code: {llm_code}")

            if llm_code:
                # 从LLM配置中获取豆包视觉参数
                llm_configs = self.conn.config.get("LLM", {})
                logger.bind(tag=TAG).info(f"可用的LLM配置: {list(llm_configs.keys())}")

                llm_config = llm_configs.get(llm_code, {})
                logger.bind(tag=TAG).info(f"LLM配置 {llm_code}: {llm_config}")

                api_key = llm_config.get("api_key", "")
                logger.bind(tag=TAG).info(f"API密钥存在: {bool(api_key)}, 长度: {len(api_key) if api_key else 0}")

                if api_key:
                    self.api_key = api_key
                    self.base_url = llm_config.get("base_url", "https://ark.cn-beijing.volces.com/api/v3")
                    self.model = llm_config.get("model_name", "ep-20241230140547-8xqzr")
                    self.max_tokens = llm_config.get("max_tokens", 1000)
                    self.temperature = llm_config.get("temperature", 0.7)

                    self.initialized = True
                    logger.bind(tag=TAG).info(f"从plugins.eye_doubao.llm_code获取配置成功，模型: {self.model}")
                    return
                else:
                    logger.bind(tag=TAG).warning(f"LLM配置 {llm_code} 中缺少API密钥")
            else:
                logger.bind(tag=TAG).info("未配置plugins.eye_doubao.llm_code")

            # 如果本地配置不可用，尝试使用fetch_llm_config_from_server获取
            if self.conn.config.get("read_config_from_api", False):
                logger.bind(tag=TAG).info("尝试使用fetch_llm_config_from_server获取豆包视觉配置...")

                # 获取设备信息
                device_id = getattr(self.conn, 'device_id', 'default')
                client_id = getattr(self.conn, 'client_id', 'default')

                logger.bind(tag=TAG).info(f"设备信息: device_id={device_id}, client_id={client_id}")

                # 查找豆包视觉配置
                eye_doubao_config = self.conn.config.get("plugins", {}).get("eye_doubao", {})
                llm_code = eye_doubao_config.get("llm_code", "")

                if llm_code:
                    logger.bind(tag=TAG).info(f"使用fetch_llm_config_from_server获取LLM配置: {llm_code}")

                    try:
                        # 使用fetch_llm_config_from_server函数
                        llm_config = fetch_llm_config_from_server(llm_code)

                        if llm_config and llm_config.get("api_key"):
                            self.api_key = llm_config.get("api_key", "")
                            self.base_url = llm_config.get("base_url", "https://ark.cn-beijing.volces.com/api/v3")
                            self.model = llm_config.get("model_name", "ep-20241230140547-8xqzr")
                            self.max_tokens = llm_config.get("max_tokens", 1000)
                            self.temperature = llm_config.get("temperature", 0.7)

                            self.initialized = True
                            logger.bind(tag=TAG).info(f"从fetch_llm_config_from_server获取配置成功，模型: {self.model}")

                            # 重新加载提示词配置，因为可能从服务器获取了新的prompt配置
                            self._load_prompts_from_server_config(llm_config)
                        else:
                            logger.bind(tag=TAG).warning(f"fetch_llm_config_from_server返回的LLM配置 {llm_code} 无效或缺少API密钥")
                    except Exception as e:
                        logger.bind(tag=TAG).error(f"调用fetch_llm_config_from_server失败: {e}")
                else:
                    logger.bind(tag=TAG).warning("未配置plugins.eye_doubao.llm_code")
            else:
                logger.bind(tag=TAG).warning("未启用API配置获取，且本地配置不可用")

        except Exception as e:
            logger.bind(tag=TAG).error(f"获取豆包视觉配置失败: {e}")

    def _load_prompts_config(self):
        """获取提示词配置"""
        try:
            logger.bind(tag=TAG).info("开始获取提示词配置...")

            # 检查LLM对象是否有自定义属性
            llm_custom_attrs = getattr(self.conn.llm, 'custom_attrs', None) if hasattr(self.conn, 'llm') else None
            logger.bind(tag=TAG).info(f"LLM自定义属性: {llm_custom_attrs}")

            if llm_custom_attrs:
                # 打印自定义属性的结构
                logger.bind(tag=TAG).info(f"LLM自定义属性类型: {type(llm_custom_attrs)}")
                logger.bind(tag=TAG).info(f"LLM自定义属性内容: {llm_custom_attrs}")

                # 尝试获取prompt1-5的配置
                prompt_attrs = {}
                for i in range(1, 6):
                    prompt_key = f"prompt{i}"
                    if prompt_key in llm_custom_attrs and llm_custom_attrs[prompt_key] and llm_custom_attrs[prompt_key].strip():
                        # 正确映射：prompt1对应索引"1"，prompt2对应索引"2"，以此类推
                        prompt_attrs[str(i)] = llm_custom_attrs[prompt_key]
                    else:
                        # 如果提示词未配置或为空，使用默认提示词
                        default_prompt = DEFAULT_PROMPTS.get(str(i))
                        if default_prompt:
                            prompt_attrs[str(i)] = default_prompt

                if prompt_attrs:
                    self.custom_prompts = prompt_attrs
                    logger.bind(tag=TAG).info(f"从LLM自定义属性获取到提示词配置(包含默认值填充): {self.custom_prompts}")
                    return

            # 如果从自定义属性中没有获取到提示词，尝试从配置中获取
            if not self.custom_prompts and self.conn and hasattr(self.conn, 'config') and self.conn.config:
                # 检查配置中是否有LLM节点
                if 'LLM' in self.conn.config:
                    # 获取当前选择的LLM模块名称
                    selected_llm = self.conn.config.get('selected_module', {}).get('LLM')
                    logger.bind(tag=TAG).info(f"当前选择的LLM模块: {selected_llm}")

                    if selected_llm and selected_llm in self.conn.config['LLM']:
                        # 获取LLM配置
                        llm_config = self.conn.config['LLM'][selected_llm]
                        logger.bind(tag=TAG).info(f"LLM配置: {llm_config}")

                        # 尝试获取prompt1-5的配置
                        prompt_attrs = {}
                        for i in range(1, 6):
                            prompt_key = f"prompt{i}"
                            if prompt_key in llm_config and llm_config[prompt_key] and llm_config[prompt_key].strip():
                                # 正确映射：prompt1对应索引"1"，prompt2对应索引"2"，以此类推
                                prompt_attrs[str(i)] = llm_config[prompt_key]
                            else:
                                # 如果提示词未配置或为空，使用默认提示词
                                default_prompt = DEFAULT_PROMPTS.get(str(i))
                                if default_prompt:
                                    prompt_attrs[str(i)] = default_prompt

                        if prompt_attrs:
                            self.custom_prompts = prompt_attrs
                            logger.bind(tag=TAG).info(f"从配置中获取到提示词配置(包含默认值填充): {self.custom_prompts}")
                            return

            # 如果都没有获取到，使用默认提示词
            if not self.custom_prompts:
                self.custom_prompts = DEFAULT_PROMPTS.copy()
                logger.bind(tag=TAG).info(f"使用默认提示词配置: {self.custom_prompts}")

        except Exception as e:
            logger.bind(tag=TAG).error(f"获取提示词配置失败: {e}")
            # 出错时使用默认提示词
            self.custom_prompts = DEFAULT_PROMPTS.copy()
            logger.bind(tag=TAG).info(f"出错时使用默认提示词配置: {self.custom_prompts}")

    def get_prompt_by_mode(self, mode):
        """根据模式获取对应的提示词"""
        try:
            # 将模式转换为字符串
            mode_str = str(mode)

            # 从自定义提示词中获取
            if mode_str in self.custom_prompts:
                prompt = self.custom_prompts[mode_str]
                logger.bind(tag=TAG).info(f"使用模式 {mode} 的自定义提示词: {prompt[:50]}...")
                return prompt

            # 如果没有找到对应模式的提示词，使用默认提示词
            if mode_str in DEFAULT_PROMPTS:
                prompt = DEFAULT_PROMPTS[mode_str]
                logger.bind(tag=TAG).info(f"使用模式 {mode} 的默认提示词: {prompt[:50]}...")
                return prompt

            # 如果模式不在1-5范围内，使用模式1的提示词
            default_prompt = self.custom_prompts.get("1") or DEFAULT_PROMPTS.get("1") or "请描述这张图片"
            logger.bind(tag=TAG).info(f"模式 {mode} 不在范围内，使用默认提示词: {default_prompt[:50]}...")
            return default_prompt

        except Exception as e:
            logger.bind(tag=TAG).error(f"获取提示词时出错: {e}")
            # 出错时返回最基本的默认提示词
            return "请描述这张图片"

    def _load_prompts_from_server_config(self, llm_config):
        """从服务器配置中加载提示词"""
        try:
            logger.bind(tag=TAG).info("从服务器配置中加载提示词...")

            # 尝试获取prompt1-5的配置
            prompt_attrs = {}
            for i in range(1, 6):
                prompt_key = f"prompt{i}"
                if prompt_key in llm_config and llm_config[prompt_key] and llm_config[prompt_key].strip():
                    # 正确映射：prompt1对应索引"1"，prompt2对应索引"2"，以此类推
                    prompt_attrs[str(i)] = llm_config[prompt_key]
                else:
                    # 如果提示词未配置或为空，使用默认提示词
                    default_prompt = DEFAULT_PROMPTS.get(str(i))
                    if default_prompt:
                        prompt_attrs[str(i)] = default_prompt

            if prompt_attrs:
                self.custom_prompts = prompt_attrs
                logger.bind(tag=TAG).info(f"从服务器配置中获取到提示词配置(包含默认值填充): {self.custom_prompts}")
            else:
                logger.bind(tag=TAG).info("服务器配置中没有提示词，保持现有配置")

        except Exception as e:
            logger.bind(tag=TAG).error(f"从服务器配置中加载提示词失败: {e}")

    async def test_connection(self):
        """测试豆包视觉API连通性"""
        try:
            if not self.initialized:
                logger.bind(tag=TAG).warning("豆包视觉提供者未初始化，跳过连通性测试")
                return False

            logger.bind(tag=TAG).info("正在测试豆包视觉API连通性...")

            # 创建一个简单的测试图片（1x1像素的白色图片）
            test_image_base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "这是一个连通性测试，请简单回复'连接正常'"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{test_image_base64}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 50,
                "temperature": 0.1
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=10)  # 连通性测试使用较短超时
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                        logger.bind(tag=TAG).info(f"豆包视觉LLM返回: {content}")
                        logger.bind(tag=TAG).info("豆包视觉API连通性测试成功")
                        return True
                    else:
                        error_text = await response.text()
                        logger.bind(tag=TAG).error(f"豆包视觉API连通性测试失败: {response.status}, {error_text}")
                        return False

        except asyncio.TimeoutError:
            logger.bind(tag=TAG).error("豆包视觉API连通性测试超时")
            return False
        except Exception as e:
            logger.bind(tag=TAG).error(f"豆包视觉API连通性测试异常: {e}")
            return False

    async def analyze_image(self, image_path, question="请描述这张图片"):
        """分析图片并返回结果"""
        try:
            # 检查是否已初始化
            if not self.initialized:
                logger.bind(tag=TAG).error("豆包视觉提供者未正确初始化")
                return None

            # 首次使用时进行连通性测试
            if not hasattr(self, '_connection_tested'):
                logger.bind(tag=TAG).info("首次使用豆包视觉，进行连通性测试...")
                is_connected = await self.test_connection()
                self._connection_tested = True
                if is_connected:
                    logger.bind(tag=TAG).info("✅ 豆包视觉API连通性测试通过")
                else:
                    logger.bind(tag=TAG).warning("⚠️  豆包视觉API连通性测试失败，但将继续尝试处理")
            # 读取图片并转换为base64
            if not os.path.exists(image_path):
                logger.bind(tag=TAG).error(f"图片文件不存在: {image_path}")
                return None

            with open(image_path, 'rb') as f:
                image_data = f.read()
            image_base64 = base64.b64encode(image_data).decode('utf-8')

            # 调用豆包视觉API
            result = await self._call_doubao_vision_api(image_base64, question)

            if result:
                logger.bind(tag=TAG).info(f"豆包视觉分析成功: {result[:100]}...")
                return result
            else:
                logger.bind(tag=TAG).error("豆包视觉分析失败")
                return None

        except Exception as e:
            logger.bind(tag=TAG).error(f"分析图片时出错: {e}")
            return None

    async def _call_doubao_vision_api(self, image_base64, question):
        """调用豆包视觉API"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": question
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{image_base64}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": self.max_tokens,
                "temperature": self.temperature
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                        return content
                    else:
                        error_text = await response.text()
                        logger.bind(tag=TAG).error(f"豆包视觉API调用失败: {response.status}, {error_text}")
                        return None

        except Exception as e:
            logger.bind(tag=TAG).error(f"调用豆包视觉API异常: {e}")
            return None


# 全局豆包视觉提供者实例缓存
_doubao_vision_providers = {}

def get_doubao_vision_provider(conn):
    """获取豆包视觉提供者实例"""
    global _doubao_vision_providers

    # 使用连接ID作为缓存键
    conn_id = getattr(conn, 'session_id', 'default')

    if conn_id not in _doubao_vision_providers:
        _doubao_vision_providers[conn_id] = DoubaoVisionProvider(conn)

    return _doubao_vision_providers[conn_id]


# 拍照识图函数描述
capture_and_analyze_image_desc = {
    "type": "function",
    "function": {
        "name": "capture_and_analyze_image",
        "description": "拍照并使用豆包视觉大模型进行图片识别",
        "parameters": {
            "type": "object",
            "properties": {
                "question": {
                    "type": "string",
                    "description": "对图片询问的问题，默认为'请描述这张图片'"
                }
            },
            "required": []
        }
    }
}

@register_function("capture_and_analyze_image", capture_and_analyze_image_desc, ToolType.IOT_CTL)
def capture_and_analyze_image(conn, question="请描述这张图片"):
    """拍照识图功能"""
    try:
        # 获取豆包视觉提供者
        vision_provider = get_doubao_vision_provider(conn)

        # 模拟图片路径（实际应该从摄像头获取）
        # 这里需要集成实际的图片获取逻辑
        image_path = "tmp/latest_camera_image.jpg"

        # 异步处理图片分析
        async def process_image():
            try:
                result = await vision_provider.analyze_image(image_path, question)
                if result:
                    # 通过TTS播报结果
                    await send_tts_message(conn, "start", result)
                    logger.bind(tag=TAG).info(f"图片识别结果已发送TTS: {result[:50]}...")
                else:
                    await send_tts_message(conn, "start", "抱歉，图片识别失败")
            except Exception as e:
                logger.bind(tag=TAG).error(f"处理图片识别时出错: {e}")
                await send_tts_message(conn, "start", "图片识别过程中发生错误")

        # 启动异步任务
        asyncio.create_task(process_image())

        return ActionResponse(
            action=Action.RESPONSE,
            result="success",
            response=f"正在拍照识图，问题：{question}"
        )

    except Exception as e:
        logger.bind(tag=TAG).error(f"拍照识图失败: {e}")
        return ActionResponse(
            action=Action.ERROR,
            result=str(e),
            response="拍照识图失败，请稍后重试"
        )


def eyes_doubaoreal(conn, mode, image_path):
    """
    豆包视觉识别主函数
    参考aiglass项目的实现

    Args:
        conn: 连接对象
        mode: 模式 (1=识图, 2=指路)
        image_path: 图片路径

    Returns:
        ActionResponse: 包含识别结果的响应对象
    """
    try:
        # 获取豆包视觉提供者
        vision_provider = get_doubao_vision_provider(conn)

        # 根据模式确定问题，使用配置的提示词
        question = vision_provider.get_prompt_by_mode(mode)

        # 异步处理图片分析
        async def process_vision():
            try:
                result = await vision_provider.analyze_image(image_path, question)
                if result:
                    # 通过TTS播报结果
                    await send_tts_message(conn, "start", result)
                    logger.bind(tag=TAG).info(f"豆包视觉识别完成: {result[:50]}...")
                    return ActionResponse(
                        action=Action.RESPONSE,
                        result=result,
                        response=result
                    )
                else:
                    error_msg = "豆包视觉识别失败"
                    await send_tts_message(conn, "start", error_msg)
                    return ActionResponse(
                        action=Action.ERROR,
                        result="vision_failed",
                        response=error_msg
                    )
            except Exception as e:
                error_msg = f"豆包视觉识别过程中发生错误: {e}"
                logger.bind(tag=TAG).error(error_msg)
                await send_tts_message(conn, "start", "视觉识别过程中发生错误")
                return ActionResponse(
                    action=Action.ERROR,
                    result=str(e),
                    response=error_msg
                )

        # 启动异步任务并返回初始响应
        asyncio.create_task(process_vision())

        return ActionResponse(
            action=Action.RESPONSE,
            result="processing",
            response="正在进行豆包视觉识别..."
        )

    except Exception as e:
        logger.bind(tag=TAG).error(f"eyes_doubaoreal失败: {e}")
        return ActionResponse(
            action=Action.ERROR,
            result=str(e),
            response="豆包视觉识别初始化失败"
        )





# 模块加载时的初始化和连通性测试
def _module_init():
    """模块加载时的初始化和连通性测试"""
    try:
        logger.bind(tag=TAG).info("🚀 豆包视觉模块已加载")

        # 尝试进行连通性测试
        try:
            import asyncio
            from config.config_loader import load_config

            # 加载配置
            config = load_config()

            # 检查是否从API获取配置
            if config.get("read_config_from_api", False):
                logger.bind(tag=TAG).info("💡 配置从管理后台API获取，尝试进行连通性测试...")

                # 即使是API配置，也要尝试连通性测试
                # 创建临时连接对象，使用默认设备信息
                class TempConnectionAPI:
                    def __init__(self, config):
                        self.config = config
                        self.device_id = "startup_test"
                        self.client_id = "startup_test"
                        self.session_id = "startup_test"

                temp_conn = TempConnectionAPI(config)

                # 尝试使用fetch_llm_config_from_server获取配置并进行连通性测试
                try:
                    # 查找豆包视觉配置
                    eye_doubao_config = config.get("plugins", {}).get("eye_doubao", {})
                    llm_code = eye_doubao_config.get("llm_code", "")

                    if llm_code:
                        logger.bind(tag=TAG).info(f"使用fetch_llm_config_from_server获取LLM配置: {llm_code}")

                        # 使用新的fetch_llm_config_from_server函数
                        llm_config = fetch_llm_config_from_server(llm_code)

                        if llm_config and llm_config.get("api_key"):
                            # 创建豆包视觉提供者进行测试
                            test_provider = DoubaoVisionProvider.__new__(DoubaoVisionProvider)
                            test_provider.conn = temp_conn
                            test_provider.api_key = llm_config.get("api_key", "")
                            test_provider.base_url = llm_config.get("base_url", "https://ark.cn-beijing.volces.com/api/v3")
                            test_provider.model = llm_config.get("model_name", "ep-20241230140547-8xqzr")
                            test_provider.max_tokens = llm_config.get("max_tokens", 1000)
                            test_provider.temperature = llm_config.get("temperature", 0.7)
                            test_provider.initialized = True

                            logger.bind(tag=TAG).info(f"使用从服务器获取的豆包视觉配置，模型: {test_provider.model}")

                            # 执行连通性测试
                            async def run_test():
                                is_connected = await test_provider.test_connection()
                                if is_connected:
                                    logger.bind(tag=TAG).info("✅ 豆包视觉API连通性测试通过")
                                else:
                                    logger.bind(tag=TAG).warning("⚠️  豆包视觉API连通性测试失败")
                                return is_connected

                            # 尝试运行测试
                            try:
                                loop = asyncio.get_event_loop()
                                if loop.is_running():
                                    # 如果已有事件循环在运行，创建任务
                                    asyncio.create_task(run_test())
                                else:
                                    # 如果没有事件循环，直接运行
                                    asyncio.run(run_test())
                            except RuntimeError:
                                # 如果无法获取事件循环，跳过测试
                                logger.bind(tag=TAG).info("🔧 连通性测试将在首次使用时进行")
                        else:
                            logger.bind(tag=TAG).warning(f"从服务器获取的LLM配置 {llm_code} 无效或缺少API密钥")
                    else:
                        logger.bind(tag=TAG).warning("未配置plugins.eye_doubao.llm_code")

                except Exception as e:
                    logger.bind(tag=TAG).error(f"使用fetch_llm_config_from_server获取配置失败: {e}")
                    # 如果从服务器获取失败，尝试使用本地DoubaoVisionProvider
                    try:
                        test_provider = DoubaoVisionProvider(temp_conn)

                        if test_provider.initialized:
                            logger.bind(tag=TAG).info(f"使用本地豆包视觉配置，模型: {test_provider.model}")

                            # 执行连通性测试
                            async def run_test():
                                is_connected = await test_provider.test_connection()
                                if is_connected:
                                    logger.bind(tag=TAG).info("✅ 豆包视觉API连通性测试通过")
                                else:
                                    logger.bind(tag=TAG).warning("⚠️  豆包视觉API连通性测试失败")
                                return is_connected

                            # 尝试运行测试
                            try:
                                loop = asyncio.get_event_loop()
                                if loop.is_running():
                                    # 如果已有事件循环在运行，创建任务
                                    asyncio.create_task(run_test())
                                else:
                                    # 如果没有事件循环，直接运行
                                    asyncio.run(run_test())
                            except RuntimeError:
                                # 如果无法获取事件循环，跳过测试
                                logger.bind(tag=TAG).info("🔧 连通性测试将在首次使用时进行")
                        else:
                            logger.bind(tag=TAG).warning("豆包视觉配置未完整，无法进行连通性测试")
                    except Exception as e2:
                        logger.bind(tag=TAG).error(f"本地配置测试也失败: {e2}")

                logger.bind(tag=TAG).info("📝 支持的功能：拍照识图")
                return

            # 按原版方式通过plugins.eye_doubao.llm_code读取配置
            logger.bind(tag=TAG).info(f"完整配置结构: plugins={config.get('plugins', {})}")

            eye_doubao_config = config.get("plugins", {}).get("eye_doubao", {})
            logger.bind(tag=TAG).info(f"eye_doubao配置: {eye_doubao_config}")

            llm_code = eye_doubao_config.get("llm_code", "")
            logger.bind(tag=TAG).info(f"获取到llm_code: '{llm_code}'")

            if llm_code:
                logger.bind(tag=TAG).info("📡 发现豆包视觉配置，开始连通性测试...")

                # 从LLM配置中获取豆包视觉参数
                llm_configs = config.get("LLM", {})
                logger.bind(tag=TAG).info(f"所有LLM配置: {list(llm_configs.keys())}")

                llm_config = llm_configs.get(llm_code, {})
                logger.bind(tag=TAG).info(f"LLM配置 '{llm_code}': {llm_config}")

                api_key = llm_config.get("api_key", "")
                logger.bind(tag=TAG).info(f"API密钥存在: {bool(api_key)}, 长度: {len(api_key) if api_key else 0}")

                if api_key:
                    # 创建临时的视觉提供者进行测试
                    class TempConnection:
                        def __init__(self, config):
                            self.config = config
                            self.device_id = "startup_test"
                            self.client_id = "startup_test"
                            self.session_id = "startup_test"

                    temp_conn = TempConnection(config)

                    # 使用LLM配置创建提供者
                    test_provider = DoubaoVisionProvider.__new__(DoubaoVisionProvider)
                    test_provider.conn = temp_conn
                    test_provider.api_key = api_key
                    test_provider.base_url = llm_config.get("base_url", "https://ark.cn-beijing.volces.com/api/v3")
                    test_provider.model = llm_config.get("model_name", "ep-20241230140547-8xqzr")
                    test_provider.max_tokens = llm_config.get("max_tokens", 1000)
                    test_provider.temperature = llm_config.get("temperature", 0.7)
                    test_provider.initialized = True

                    logger.bind(tag=TAG).info(f"使用豆包视觉配置，模型: {test_provider.model}")

                    # 执行连通性测试
                    async def run_test():
                        is_connected = await test_provider.test_connection()
                        if is_connected:
                            logger.bind(tag=TAG).info("✅ 豆包视觉API连通性测试通过")
                        else:
                            logger.bind(tag=TAG).warning("⚠️  豆包视觉API连通性测试失败")
                        return is_connected

                    # 尝试运行测试
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # 如果已有事件循环在运行，创建任务
                            asyncio.create_task(run_test())
                        else:
                            # 如果没有事件循环，直接运行
                            asyncio.run(run_test())
                    except RuntimeError:
                        # 如果无法获取事件循环，跳过测试
                        logger.bind(tag=TAG).info("🔧 连通性测试将在首次使用时进行")
                else:
                    logger.bind(tag=TAG).warning(f"LLM配置 {llm_code} 中缺少API密钥")
            else:
                logger.bind(tag=TAG).info("💡 未配置plugins.eye_doubao.llm_code，请配置后重启")

        except Exception as e:
            logger.bind(tag=TAG).info(f"💡 豆包视觉配置检查: {e}")

        logger.bind(tag=TAG).info("📝 支持的功能：拍照识图")

    except Exception as e:
        logger.bind(tag=TAG).info(f"豆包视觉模块初始化: {e}")


# 模块加载时执行初始化
try:
    _module_init()
except Exception as e:
    # 静默处理，不影响模块加载
    pass
